<?php

namespace App\Services\Frontend;

use App\Enums\ConferenceStatus;
use App\Models\Advertisement;
use App\Models\Category;
use App\Models\Country;
use App\Models\Event;
use App\Models\News;
use App\Services\CategoryService;
use App\Services\CountryService;
use App\Services\SeoService; // 引入SeoService
use App\Exceptions\CategoryNotFoundException;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;

class HomeService
{
    /**
     * 分类服务实例
     */
    protected CategoryService $categoryService;

    /**
     * 国家/地区服务实例
     */
    protected CountryService $countryService;

    /**
     * SEO服务实例
     */
    protected SeoService $seoService;

    /**
     * 构造函数，注入分类服务、国家地区服务和SEO服务
     * 
     * @param CategoryService $categoryService
     * @param CountryService $countryService
     * @param SeoService $seoService
     */
    public function __construct(CategoryService $categoryService, CountryService $countryService, SeoService $seoService)
    {
        $this->categoryService = $categoryService;
        $this->countryService = $countryService;
        $this->seoService = $seoService;
    }

    /**
     * 获取首页数据
     * 
     * @return array 首页所需的所有数据
     */
    public function getHomePageData(): array
    {
        // 获取一级分类（使用缓存服务）
        $categories = $this->getRootCategories();

        // 获取即将召开的会议（一个月内）
        $upcomingConferences = $this->getUpcomingConferences();

        // 获取推荐会议
        $recommendedConferences = $this->getRecommendedConferences();

        // 获取广告
        $advertisements = $this->getHomeAdvertisements();

        // 获取国家/地区（一级）
        $countries = $this->getRootCountries();

        // 获取新闻列表（带分页）
        $news = $this->getLatestNews();

        // 获取特色新闻（带图片）
        $featuredNews = $this->getFeaturedNews();

        // 获取最新发布的会议
        $latestConferences = $this->getLatestPublishedConferences();

        // SEO信息
        $seo = $this->seoService->getSeoData('home');

        return [
            'categories' => $categories,
            'upcomingConferences' => $upcomingConferences,
            'recommendedConferences' => $recommendedConferences,
            'advertisements' => $advertisements,
            'countries' => $countries,
            'news' => $news,
            'featuredNews' => $featuredNews,
            'latestConferences' => $latestConferences,
            'seo' => $seo
        ];
    }

    /**
     * 获取一级分类（使用缓存服务）
     * 
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRootCategories()
    {
        // 使用缓存服务获取顶级分类
        return $this->categoryService->getTopCategories();
    }

    /**
     * 获取即将召开的会议
     * 
     * @param int $limit 限制数量
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getUpcomingConferences(int $limit = 7)
    {
        $oneMonthLater = strtotime('+1 month');

        return Event::published()
            ->where('end_date', '>=', $oneMonthLater)
            ->orderBy('ding', 'desc')
            ->orderBy('end_date', 'asc')
            ->with(['country:id,venue', 'categories:id,name']) // 预加载关联，避免N+1查询
            ->limit($limit)
            ->get();
    }

    /**
     * 获取推荐会议
     *
     * @param int $limit 限制数量
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRecommendedConferences(int $limit = 8)
    {
        return Event::published()
            ->recommended()
            ->orderBy('id', 'desc')
            ->with(['country:id,venue', 'categories:id,name']) // 预加载关联，避免N+1查询
            ->limit($limit)
            ->get();
    }

    /**
     * 获取最新发布的会议
     *
     * @param int $limit 限制数量
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getLatestPublishedConferences(int $limit = 8)
    {
        return Event::published()
            ->orderBy('addtime', 'desc')
            ->with(['country:id,venue', 'categories:id,name']) // 预加载关联，避免N+1查询
            ->limit($limit)
            ->get();
    }

    /**
     * 获取首页广告
     * 
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getHomeAdvertisements()
    {
        return Advertisement::where('cid', 0)
            ->where('endtime', '>=', time())
            ->get();
    }

    /**
     * 获取一级国家/地区(带会议数量统计)
     * 
     * @return \Illuminate\Support\Collection 国家/地区列表
     */
    protected function getRootCountries()
    {
        // 使用country service获取已缓存的国家列表(带会议数量)
        return $this->countryService->getContinentsWithEventCount();
    }

    /**
     * 获取最新新闻（带分页）
     * 
     * @param int $perPage 每页数量
     * @return LengthAwarePaginator
     */
    public function getLatestNews(int $perPage = 8)
    {
        return News::orderBy('id', 'desc')
            ->paginate($perPage);
    }

    /**
     * 获取特色新闻（带图片）
     * 
     * @param int $limit 限制数量
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getFeaturedNews(int $limit = 3)
    {
        $featuredNews = News::where('is_featured', true)
            ->whereNotNull('cover')
            ->where('cover', '!=', '')
            ->orderBy('id', 'desc')
            ->limit($limit)
            ->get();

        // 处理封面图
        foreach ($featuredNews as $news) {
            if (empty($news->cover)) {
                $news->cover = "/images/nopic.jpg"; // 默认没有图片的路径
            }
        }

        return $featuredNews;
    }

    /**
     * 根据分类URL获取分类信息及其会议列表
     *
     * @param string $url 分类URL
     * @param int $perPage 每页数量
     * @return array 分类信息和会议列表
     * @throws \Exception 当分类不存在时抛出异常
     */
    public function getCategoryConferences(string $url, int $perPage = 15): array
    {
        try {
            // 查找分类
            $category = $this->findCategoryByUrl($url);

            // 获取分类ID列表
            $categoryIds = $this->getCategoryIds($category);

            // 获取会议列表
            $conferences = $this->getConferencesByCategory($categoryIds, $perPage);

            // 获取特色会议
            $featuredConferences = $this->getFeaturedConferencesByCategory($categoryIds);

            // 获取广告
            $advertisements = $this->getCategoryAdvertisements($category->id);

            // 获取SEO信息
            $seo = $this->getCategorySeoData($category);

            return [
                'category' => $category,
                'conferences' => $conferences,
                'featuredConferences' => $featuredConferences,
                'advertisements' => $advertisements,
                'seo' => $seo
            ];
        } catch (\Exception $e) {
            Log::error('getCategoryConferences error: ' . $e->getMessage(), [
                'url' => $url,
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 根据URL查找分类
     *
     * @param string $url
     * @return Category
     * @throws \Exception
     */
    private function findCategoryByUrl(string $url): Category
    {
        $trimmedUrl = trim($url);

        Log::info('Finding category by URL', ['url' => $trimmedUrl]);

        // 优先从缓存中查找
        $allCategories = $this->categoryService->getTree()->flatten(1);
        $category = $allCategories->firstWhere('url', $trimmedUrl);

        // 尝试URL解码后查找
        if (!$category) {
            $decodedUrl = urldecode($trimmedUrl);
            if ($decodedUrl !== $trimmedUrl) {
                $category = $allCategories->firstWhere('url', $decodedUrl);
                Log::info('Trying decoded URL', ['decoded_url' => $decodedUrl, 'found' => !!$category]);
            }
        }

        if (!$category) {
            Log::warning('Category not found', ['url' => $trimmedUrl]);
            throw new CategoryNotFoundException($trimmedUrl);
        }

        Log::info('Category found', ['id' => $category->id, 'name' => $category->name]);
        return $category;
    }

    /**
     * 获取分类ID列表（包括子分类）
     *
     * @param Category $category
     * @return array
     */
    private function getCategoryIds(Category $category): array
    {
        if ($category->fid == 0) {
            // 顶级分类：获取所有子分类ID
            $categoryTree = $this->categoryService->getTree();
            $topCategory = $categoryTree->firstWhere('id', $category->id);

            if ($topCategory && $topCategory->children && $topCategory->children->count() > 0) {
                $categoryIds = $topCategory->children->pluck('id')->toArray();
                Log::info('Top category with children', ['category_id' => $category->id, 'child_ids' => $categoryIds]);
                return $categoryIds;
            } else {
                // 如果顶级分类没有子分类，返回自身ID
                Log::info('Top category without children, using self ID', ['category_id' => $category->id]);
                return [$category->id];
            }
        } else {
            // 子分类：只返回当前分类ID
            Log::info('Sub category', ['category_id' => $category->id]);

            // 为子分类添加同级分类信息
            $this->attachSiblingCategories($category);

            return [$category->id];
        }
    }

    /**
     * 为子分类附加同级分类信息
     *
     * @param Category $category
     */
    private function attachSiblingCategories(Category $category): void
    {
        $categoryTree = $this->categoryService->getTree();
        $parentCategory = $categoryTree->firstWhere('id', $category->fid);

        if ($parentCategory && $parentCategory->children && $parentCategory->children->count() > 0) {
            $category->siblings = $parentCategory->children->sortBy('listorder');
        }
    }

    /**
     * 获取分类下的会议列表
     *
     * @param array $categoryIds
     * @param int $perPage
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    private function getConferencesByCategory(array $categoryIds, int $perPage)
    {
        if (empty($categoryIds)) {
            Log::info('Empty category IDs, returning empty paginator');
            return new LengthAwarePaginator([], 0, $perPage, 1, [
                'path' => request()->url(),
                'pageName' => 'page',
            ]);
        }

        try {
            Log::info('Building conference query', ['category_ids' => $categoryIds]);

            // 首先检查是否有会议存在于这些分类中
            $hasConferences = \DB::table('list')
                ->whereIn('cid', $categoryIds)
                ->exists();

            if (!$hasConferences) {
                Log::info('No conferences found for categories', ['category_ids' => $categoryIds]);
                return new LengthAwarePaginator([], 0, $perPage, 1, [
                    'path' => request()->url(),
                    'pageName' => 'page',
                ]);
            }

            // 构建优化的查询
            $query = Event::published()
                ->join('list', 'event.id', '=', 'list.eid')
                ->whereIn('list.cid', $categoryIds)
                ->select('event.*')
                ->distinct()
                ->with(['country:id,venue'])
                ->orderBy('event.end_date', 'desc');

            // 安全的分页查询
            $conferences = $query->paginate($perPage, ['*'], 'page', request()->get('page', 1));

            // 手动加载分类关系
            $this->loadConferenceCategories($conferences);

            Log::info('Conferences loaded successfully', [
                'count' => $conferences->count(),
                'total' => $conferences->total(),
                'current_page' => $conferences->currentPage(),
                'last_page' => $conferences->lastPage()
            ]);

            return $conferences;
        } catch (\Exception $e) {
            Log::error('Error loading conferences', [
                'error' => $e->getMessage(),
                'category_ids' => $categoryIds,
                'trace' => $e->getTraceAsString()
            ]);

            // 返回安全的空分页器
            return new LengthAwarePaginator([], 0, $perPage, 1, [
                'path' => request()->url(),
                'pageName' => 'page',
            ]);
        }
    }

    /**
     * 获取分类下的特色会议
     *
     * @param array $categoryIds
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getFeaturedConferencesByCategory(array $categoryIds)
    {
        if (empty($categoryIds)) {
            Log::info('Empty category IDs for featured conferences');
            return collect([]);
        }

        try {
            $now = time();

            // 首先检查是否有特色会议
            $hasFeaturedConferences = \DB::table('event')
                ->join('list', 'event.id', '=', 'list.eid')
                ->where('event.status', 1) // published
                ->where('event.is_featured', 1)
                ->where('event.end_date', '>=', $now)
                ->whereIn('list.cid', $categoryIds)
                ->exists();

            if (!$hasFeaturedConferences) {
                Log::info('No featured conferences found for categories', ['category_ids' => $categoryIds]);
                return collect([]);
            }

            $featuredConferences = Event::published()
                ->featured()
                ->where('event.end_date', '>=', $now)
                ->join('list', 'event.id', '=', 'list.eid')
                ->whereIn('list.cid', $categoryIds)
                ->select('event.*')
                ->distinct()
                ->with(['country:id,venue'])
                ->orderBy('event.end_date', 'asc')
                ->limit(5)
                ->get();

            // 手动加载分类关系
            $this->loadConferenceCategories($featuredConferences);

            Log::info('Featured conferences loaded', ['count' => $featuredConferences->count()]);

            return $featuredConferences;
        } catch (\Exception $e) {
            Log::error('Error loading featured conferences', [
                'error' => $e->getMessage(),
                'category_ids' => $categoryIds,
                'trace' => $e->getTraceAsString()
            ]);
            return collect([]);
        }
    }

    /**
     * 为会议加载分类关系
     *
     * @param \Illuminate\Database\Eloquent\Collection|\Illuminate\Pagination\LengthAwarePaginator $conferences
     */
    private function loadConferenceCategories($conferences): void
    {
        $eventIds = $conferences->pluck('id')->toArray();

        if (empty($eventIds)) {
            return;
        }

        // 获取所有分类映射
        $allCategories = $this->categoryService->getTree()->flatten(1);
        $categoryMap = $allCategories->keyBy('id');

        // 获取会议分类关系
        $eventCategoryRelations = \DB::table('list')
            ->whereIn('eid', $eventIds)
            ->get(['eid', 'cid']);

        // 构建分类映射
        $eventCategoriesMap = [];
        foreach ($eventCategoryRelations as $relation) {
            if (!isset($eventCategoriesMap[$relation->eid])) {
                $eventCategoriesMap[$relation->eid] = [];
            }
            if ($categoryMap->has($relation->cid)) {
                $eventCategoriesMap[$relation->eid][] = $categoryMap[$relation->cid];
            }
        }

        // 附加分类到会议对象
        foreach ($conferences as $conference) {
            $conference->categories = collect($eventCategoriesMap[$conference->id] ?? []);
        }
    }

    /**
     * 获取分类广告
     *
     * @param int $categoryId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getCategoryAdvertisements(int $categoryId)
    {
        $now = time();
        return Advertisement::where('cid', $categoryId)
            ->where('endtime', '>=', $now)
            ->orderBy('id', 'desc')
            ->limit(5)
            ->get();
    }

    /**
     * 获取分类SEO数据
     *
     * @param Category $category
     * @return array
     */
    private function getCategorySeoData(Category $category): array
    {
        $placeholders = [
            'category_name' => $category->name,
            'site_name' => 'iConf'
        ];

        return $this->seoService->getSeoData('conferences_list', $placeholders);
    }

    /**
     * 获取所有会议列表（带搜索功能）
     * 
     * @param string|null $title 搜索关键词
     * @param int $perPage 每页数量
     * @return array 会议列表和相关数据
     */
    public function getAllConferences(?string $keyword = null, ?string $filterCategory = null, ?string $filterCountry = null, ?string $filterYear = null, int $perPage = 15): array
    {
        $now = time();

        // 从缓存获取所有分类数据
        $allCategories = $this->categoryService->getTree()->flatten(1);
        $categoryMap = $allCategories->keyBy('id');

        // 构建查询条件 - 只预加载国家，分类将手动处理
        $query = Event::published()->with(['country:id,venue']);

        // 如果有搜索关键词
        if ($keyword) {
            $query->where(function ($q) use ($keyword) {
                $q->where('title', 'like', '%' . $keyword . '%')
                    ->orWhere('event', 'like', '%' . $keyword . '%')
                    ->orWhere('summary', 'like', '%' . $keyword . '%');
            });
        }

        // 如果有分类筛选 - 使用JOIN替代whereHas提升性能
        if ($filterCategory) {
            $category = $allCategories->firstWhere('url', $filterCategory);
            if ($category) {
                $categoryIds = [$category->id];
                // 如果是顶级分类，包含其所有子分类
                if ($category->fid == 0 && $category->children) {
                    $categoryIds = array_merge($categoryIds, $category->children->pluck('id')->toArray());
                }
                // 使用JOIN替代whereHas，大幅提升查询性能
                $query->join('list', 'event.id', '=', 'list.eid')
                    ->whereIn('list.cid', $categoryIds)
                    ->select('event.*')  // 添加这一行确保只返回event表的字段
                    ->distinct();
            }
        }

        // 如果有国家筛选 - 使用缓存避免重复查询
        if ($filterCountry) {
            // 使用静态缓存避免重复查询
            static $countryCache = null;
            if ($countryCache === null) {
                $countryCache = Country::all()->keyBy('url');
            }

            $country = $countryCache->get($filterCountry);
            if ($country) {
                $countryIds = [$country->id];
                // 如果是顶级国家（大洲），包含其所有子国家
                if ($country->fid == 0) {
                    $childCountries = Country::where('fid', $country->id)->pluck('id')->toArray();
                    $countryIds = array_merge($countryIds, $childCountries);
                }
                $query->whereIn('event.venue', $countryIds);
            }
        }

        // 如果有年份筛选
        if ($filterYear) {
            $yearStart = strtotime($filterYear . '-01-01 00:00:00');
            $yearEnd = strtotime($filterYear . '-12-31 23:59:59');
            $query->whereBetween('start_date', [$yearStart, $yearEnd]);
        }

        // 使用数据库级分页，大幅提升性能
        // 优化排序：即将召开的会议优先，按结束时间排序
        $conferences = $query
            ->orderByRaw('CASE WHEN event.end_date > ? THEN 0 ELSE 1 END', [$now])
            ->orderByRaw('CASE WHEN event.end_date > ? THEN event.end_date ELSE -event.end_date END', [$now])
            ->paginate($perPage);

        // 只为当前页的会议加载分类关系，避免N+1查询
        $eventIds = $conferences->pluck('id')->toArray();
        if (!empty($eventIds)) {
            $eventCategoryRelations = \DB::table('list')
                ->whereIn('eid', $eventIds)
                ->get(['eid', 'cid']);

            // 为每个会议分配其分类
            $eventCategoriesMap = [];
            foreach ($eventCategoryRelations as $relation) {
                if (!isset($eventCategoriesMap[$relation->eid])) {
                    $eventCategoriesMap[$relation->eid] = [];
                }
                // 只有当分类存在于缓存中时才添加
                if ($categoryMap->has($relation->cid)) {
                    $eventCategoriesMap[$relation->eid][] = $categoryMap[$relation->cid];
                }
            }

            // 将分类附加到会议对象
            foreach ($conferences as $conference) {
                $conference->categories = collect($eventCategoriesMap[$conference->id] ?? []);
            }
        }

        // 获取筛选选项数据（使用缓存）
        $categories = $this->categoryService->getTopCategories();
        $countries = $this->countryService->getTopLevelCountries();

        $filterOptions = [
            'categories' => $categories,
            'countries' => $countries,
            'years' => $this->getAvailableYears(),
        ];

        return [
            'conferences' => $conferences,
            'filterOptions' => $filterOptions,
            'currentFilters' => [
                'keyword' => $keyword,
                'category' => $filterCategory,
                'country' => $filterCountry,
                'year' => $filterYear,
            ],
        ];
    }

    /**
     * 获取按地点筛选的会议数据
     * 
     * @param string $url 地点URL标识
     * @param int $perPage 每页显示记录数
     * @return array 按地点筛选的会议数据
     * @throws \Exception 如果地点不存在
     */
    public function getVenueConferences(string $url, int $perPage = 10): array
    {
        // 根据URL标识查找地点
        $country = Country::where('url', $url)->firstOrFail();

        // 获取该地点及其子地点的ID列表
        $countryIds = [$country->id];
        $isContinent = false;
        $childCountries = collect();

        // 判断是否为大洲（顶级地区）
        if ($country->fid === 0) {

            $isContinent = true;

            // 获取大洲下的所有国家
            $childCountries = Country::where('fid', $country->id)
                ->select(['id', 'venue', 'url'])
                ->orderBy('listorder')
                ->get();



            // 将所有子国家ID添加到查询列表
            $countryIds = array_merge($countryIds, $childCountries->pluck('id')->toArray());
        }

        // 获取当前时间
        $now = time();

        // 基本查询条件
        $query = Event::published()
            ->whereIn('venue', $countryIds) // venue字段存储的是Country表的id
            ->with(['country:id,venue,url']);

        // 获取即将召开的会议
        $upcomingConferences = $query->where('end_date', '>=', $now)
            ->orderBy('end_date', 'asc')
            ->get();

        // 获取已结束的会议
        $pastQuery = clone $query;
        $pastConferences = $pastQuery->where('end_date', '<=', $now)
            ->orderBy('end_date', 'desc')
            ->get();

        // 合并会议列表
        $allConferences = $upcomingConferences->concat($pastConferences);

        // 手动分页
        $page = request()->get('page', 1);
        $offset = ($page - 1) * $perPage;
        $slicedConferences = $allConferences->slice($offset, $perPage);
        $conferences = new LengthAwarePaginator(
            $slicedConferences,
            $allConferences->count(),
            $perPage,
            $page,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        // 获取该地点下的特色会议
        $featuredConferences = Event::published()
            ->featured()
            ->where('end_date', '>=', $now)
            ->whereIn('venue', $countryIds) // venue存储的是Country表的id
            ->with(['country:id,venue,url'])
            ->orderBy('end_date', 'asc')
            ->get();

        // 获取广告
        $advertisements = Advertisement::where('cid', 0) // 使用首页广告
            ->where('endtime', '>=', $now)
            ->orderBy('id', 'desc')
            ->get();

        // 获取一级分类
        $categories = Category::where('fid', 0)->get();

        // SEO信息
        $placeholders = [
            'venue_name' => $country->venue,
            'site_name' => 'iConf'
        ];
        $seo = $this->seoService->getSeoData('conferences_list', $placeholders);

        return [
            'country' => $country,
            'conferences' => $conferences,
            'featuredConferences' => $featuredConferences,
            'advertisements' => $advertisements,
            'categories' => $categories,
            'childCountries' => $childCountries,  // 大洲的子国家列表
            'isContinent' => $isContinent,        // 是否为大洲
            'totalCount' => $allConferences->count(), // 会议总数
            'seo' => $seo
        ];
    }

    /**
     * 获取可用的年份列表（用于筛选）
     *
     * @return array 年份数组
     */
    private function getAvailableYears(): array
    {
        // 使用缓存避免重复查询
        static $years = null;
        if ($years === null) {
            $currentYear = date('Y');
            $years = [];

            // 提供当前年份前后各3年的选项
            for ($i = -2; $i <= 3; $i++) {
                $year = $currentYear + $i;
                $years[] = [
                    'value' => $year,
                    'label' => $year,
                ];
            }
        }

        return $years;
    }
}
