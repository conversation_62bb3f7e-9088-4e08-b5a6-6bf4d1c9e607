<?php

namespace App\Services\Frontend;

use App\Enums\ConferenceStatus;
use App\Models\Advertisement;
use App\Models\Category;
use App\Models\Country;
use App\Models\Event;
use App\Models\News;
use App\Services\CategoryService;
use App\Services\CountryService;
use App\Services\SeoService; // 引入SeoService
use Illuminate\Pagination\LengthAwarePaginator;

class HomeService
{
    /**
     * 分类服务实例
     */
    protected CategoryService $categoryService;
    
    /**
     * 国家/地区服务实例
     */
    protected CountryService $countryService;

    /**
     * SEO服务实例
     */
    protected SeoService $seoService;

    /**
     * 构造函数，注入分类服务、国家地区服务和SEO服务
     * 
     * @param CategoryService $categoryService
     * @param CountryService $countryService
     * @param SeoService $seoService
     */
    public function __construct(CategoryService $categoryService, CountryService $countryService, SeoService $seoService)
    {
        $this->categoryService = $categoryService;
        $this->countryService = $countryService;
        $this->seoService = $seoService;
    }

    /**
     * 获取首页数据
     * 
     * @return array 首页所需的所有数据
     */
    public function getHomePageData(): array
    {
        // 获取一级分类（使用缓存服务）
        $categories = $this->getRootCategories();

        // 获取即将召开的会议（一个月内）
        $upcomingConferences = $this->getUpcomingConferences();

        // 获取推荐会议
        $recommendedConferences = $this->getRecommendedConferences();

        // 获取广告
        $advertisements = $this->getHomeAdvertisements();

        // 获取国家/地区（一级）
        $countries = $this->getRootCountries();

        // 获取新闻列表（带分页）
        $news = $this->getLatestNews();

        // 获取特色新闻（带图片）
        $featuredNews = $this->getFeaturedNews();

        // 获取最新发布的会议
        $latestConferences = $this->getLatestPublishedConferences();

        // SEO信息
        $seo = $this->seoService->getSeoData('home');

        return [
            'categories' => $categories,
            'upcomingConferences' => $upcomingConferences,
            'recommendedConferences' => $recommendedConferences,
            'advertisements' => $advertisements,
            'countries' => $countries,
            'news' => $news,
            'featuredNews' => $featuredNews,
            'latestConferences' => $latestConferences,
            'seo' => $seo
        ];
    }

    /**
     * 获取一级分类（使用缓存服务）
     * 
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRootCategories()
    {
        // 使用缓存服务获取顶级分类
        return $this->categoryService->getTopCategories();
    }

    /**
     * 获取即将召开的会议
     * 
     * @param int $limit 限制数量
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getUpcomingConferences(int $limit = 7)
    {
        $oneMonthLater = strtotime('+1 month');

        return Event::published()
            ->where('end_date', '>=', $oneMonthLater)
            ->orderBy('ding', 'desc')
            ->orderBy('end_date', 'asc')
            ->with(['country:id,venue', 'categories:id,name']) // 预加载关联，避免N+1查询
            ->limit($limit)
            ->get();
    }

    /**
     * 获取推荐会议
     *
     * @param int $limit 限制数量
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRecommendedConferences(int $limit = 8)
    {
        return Event::published()
            ->recommended()
            ->orderBy('id', 'desc')
            ->with(['country:id,venue', 'categories:id,name']) // 预加载关联，避免N+1查询
            ->limit($limit)
            ->get();
    }

    /**
     * 获取最新发布的会议
     *
     * @param int $limit 限制数量
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getLatestPublishedConferences(int $limit = 8)
    {
        return Event::published()
            ->orderBy('addtime', 'desc')
            ->with(['country:id,venue', 'categories:id,name']) // 预加载关联，避免N+1查询
            ->limit($limit)
            ->get();
    }

    /**
     * 获取首页广告
     * 
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getHomeAdvertisements()
    {
        return Advertisement::where('cid', 0)
            ->where('endtime', '>=', time())
            ->get();
    }

    /**
     * 获取一级国家/地区(带会议数量统计)
     * 
     * @return \Illuminate\Support\Collection 国家/地区列表
     */
    protected function getRootCountries()
    {
        // 使用country service获取已缓存的国家列表(带会议数量)
        return $this->countryService->getContinentsWithEventCount();
    }

    /**
     * 获取最新新闻（带分页）
     * 
     * @param int $perPage 每页数量
     * @return LengthAwarePaginator
     */
    public function getLatestNews(int $perPage = 8)
    {
        return News::orderBy('id', 'desc')
            ->paginate($perPage);
    }

    /**
     * 获取特色新闻（带图片）
     * 
     * @param int $limit 限制数量
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getFeaturedNews(int $limit = 3)
    {
        $featuredNews = News::where('is_featured', true)
            ->whereNotNull('cover')
            ->where('cover', '!=', '')
            ->orderBy('id', 'desc')
            ->limit($limit)
            ->get();

        // 处理封面图
        foreach ($featuredNews as $key => $news) {
            if (empty($news->cover)) {
                $news->cover = "/images/nopic.jpg"; // 默认没有图片的路径
            }
        }

        return $featuredNews;
    }

    /**
     * 根据分类URL获取分类信息及其会议列表
     * 
     * @param string $url 分类URL
     * @param int $perPage 每页数量
     * @return array 分类信息和会议列表
     */
    public function getCategoryConferences(string $url, int $perPage = 15): array
    {
        // 缓存当前时间以避免多次调用 time()
        $now = time();

        // 直接从数据库获取所有分类，并按 listorder 排序
        // 这样可以确保 $allCategories 包含所有分类，且是扁平的
        $allCategories = Category::orderBy('listorder', 'asc')->get();

        $categoryMap = $allCategories->keyBy('id');
       

        // 调试日志：输出所有分类的URL和传入的URL
        \Log::info('--- Debugging getCategoryConferences ---');
        \Log::info('All category URLs from DB: ' . json_encode($allCategories->pluck('url')->toArray()));
        \Log::info('Incoming URL (raw): ' . $url);

        // 对传入的URL进行trim()处理，以防有空白字符
        $trimmedUrl = trim($url);
        \Log::info('Incoming URL (trimmed): ' . $trimmedUrl);

        // 从集合中查找匹配URL的分类
        $category = $allCategories->firstWhere('url', $trimmedUrl);

        // 再次尝试解码URL后查找，以防编码问题
        if (!$category) {
            $decodedUrl = urldecode($trimmedUrl);
            if ($decodedUrl !== $trimmedUrl) { // 只有当解码后URL不同时才尝试
                $category = $allCategories->firstWhere('url', $decodedUrl);
                \Log::info('Attempting with decoded URL: ' . $decodedUrl . ' - Found: ' . ($category ? 'true' : 'false'));
            }
        }

        // 调试日志：输出找到的分类信息
        if ($category) {
            \Log::info('Category found: ID=' . $category->id . ', URL=' . $category->url);
        } else {
            \Log::info('Category NOT found for URL: ' . $trimmedUrl);
        }
        dd

        if (!$category) {
            abort(404, '分类不存在或已被删除');
        }

        // 获取分类树用于查找子分类
        $categoryTree = $this->categoryService->getTree();

        // 优化分类ID查询，始终使用缓存并确保按listorder排序
        if ($category->fid == 0) {
            // 顶级分类：使用缓存的子分类，确保按listorder排序
            // 从缓存中获取子分类，不再从数据库中直接获取
            $childCategories = collect();

            // 从缓存的分类树中找到对应的顶级分类
            $topCategory = $categoryTree->firstWhere('id', $category->id);

            if ($topCategory && $topCategory->children && $topCategory->children->count() > 0) {
                // 确保子分类按照listorder排序
                $childCategories = $topCategory->children->sortBy('listorder');
            }

            $categoryIds = $childCategories->pluck('id')->toArray();

            // 如果没有子分类，返回空结果
            if (empty($categoryIds)) {
                $name = $category->name;
                $placeholders = [
                    'category_name' => $name,
                    'site_name' => 'iConf'
                ];

                return [
                    'category' => $category,
                    'conferences' => new \Illuminate\Pagination\LengthAwarePaginator([], 0, isset($perPage) ? $perPage : 15),
                    'featuredConferences' => collect([]),
                    'advertisements' => collect([]),
                    'seo' => $this->seoService->getSeoData('conferences_list', $placeholders),
                ];
            }
        } else {
            // 子分类：只查询当前分类的会议
            $categoryIds = [$category->id];

            // 在返回的数据中添加同级分类数据，方便前台侧边栏显示并排序
            // 从缓存中找到父分类
            $parentCategory = $allCategories->firstWhere('id', $category->fid);
            if ($parentCategory) {
                // 如果从父分类下的缓存children集合找到了同级分类，则按listorder排序后赋值给category
                $topCategory = $categoryTree->firstWhere('id', $parentCategory->id);
                if ($topCategory && $topCategory->children && $topCategory->children->count() > 0) {
                    // 将按listorder排序好的同级分类附加到当前分类对象，便于前台访问
                    $category->siblings = $topCategory->children->sortBy('listorder');
                }
            }
        }

        // 优化会议查询，使用 join 而非 whereHas，并且不预加载分类
        $conferenceQuery = Event::published()
            ->join('list', 'event.id', '=', 'list.eid')
            ->whereIn('list.cid', $categoryIds)
            ->select('event.*')
            ->distinct()
            ->with(['country:id,venue']) // 只预加载国家，不预加载分类
            ->orderBy('event.end_date', 'desc');

        // 获取分页结果
        $conferences = $conferenceQuery->paginate($perPage);

        // 手动加载会议分类关系，避免N+1查询
        $eventIds = $conferences->pluck('id')->toArray();
        $eventCategoryRelations = \DB::table('list')
            ->whereIn('eid', $eventIds)
            ->get(['eid', 'cid']);

        // 为每个会议分配其分类
        $eventCategoriesMap = [];
        foreach ($eventCategoryRelations as $relation) {
            if (!isset($eventCategoriesMap[$relation->eid])) {
                $eventCategoriesMap[$relation->eid] = [];
            }
            // 只有当分类存在于缓存中时才添加
            if ($categoryMap->has($relation->cid)) {
                $eventCategoriesMap[$relation->eid][] = $categoryMap[$relation->cid];
            }
        }

        // 将分类附加到会议对象
        foreach ($conferences as $conference) {
            $conference->categories = collect($eventCategoriesMap[$conference->id] ?? []);
        }

        // 优化特色会议查询（使用is_featured=1），同样使用 join 并手动处理分类
        $featuredConferences = Event::published()
            ->featured() // 使用特色会议作用域（is_featured=1）
            ->where('event.end_date', '>=', $now)
            ->join('list', 'event.id', '=', 'list.eid')
            ->whereIn('list.cid', $categoryIds)
            ->select('event.*')
            ->distinct()
            ->with(['country:id,name,venue']) // 只预加载国家
            ->orderBy('event.end_date', 'asc')
            ->limit(5) // 限制特色会议数量
            ->get();

        // 手动加载特色会议分类关系
        $featuredEventIds = $featuredConferences->pluck('id')->toArray();
        $featuredEventCategoryRelations = \DB::table('list')
            ->whereIn('eid', $featuredEventIds)
            ->get(['eid', 'cid']);

        // 为每个特色会议分配其分类
        $featuredEventCategoriesMap = [];
        foreach ($featuredEventCategoryRelations as $relation) {
            if (!isset($featuredEventCategoriesMap[$relation->eid])) {
                $featuredEventCategoriesMap[$relation->eid] = [];
            }
            if ($categoryMap->has($relation->cid)) {
                $featuredEventCategoriesMap[$relation->eid][] = $categoryMap[$relation->cid];
            }
        }

        // 将分类附加到特色会议对象
        foreach ($featuredConferences as $conference) {
            $conference->categories = collect($featuredEventCategoriesMap[$conference->id] ?? []);
        }

        // 优化广告查询，添加限制
        $advertisements = Advertisement::where('cid', $category->id)
            ->where('endtime', '>=', $now)
            ->orderBy('id', 'desc')
            ->limit(5) // 限制广告数量
            ->get();

        // SEO信息
        $placeholders = [
            'category_name' => $category->name,
            'site_name' => 'iConf'
        ];
        $seo = $this->seoService->getSeoData('conferences_list', $placeholders);

        return [
            'category' => $category,
            'conferences' => $conferences,
            'featuredConferences' => $featuredConferences,
            'advertisements' => $advertisements,
            'seo' => $seo
        ];
    }

    /**
     * 获取所有会议列表（带搜索功能）
     * 
     * @param string|null $title 搜索关键词
     * @param int $perPage 每页数量
     * @return array 会议列表和相关数据
     */
    public function getAllConferences(?string $keyword = null, ?string $filterCategory = null, ?string $filterCountry = null, ?string $filterYear = null, int $perPage = 15): array
    {
        $now = time();

        // 从缓存获取所有分类数据
        $allCategories = $this->categoryService->getTree()->flatten(1);
        $categoryMap = $allCategories->keyBy('id');

        // 构建查询条件 - 只预加载国家，分类将手动处理
        $query = Event::published()->with(['country:id,venue']);

        // 如果有搜索关键词
        if ($keyword) {
            $query->where(function ($q) use ($keyword) {
                $q->where('title', 'like', '%' . $keyword . '%')
                    ->orWhere('event', 'like', '%' . $keyword . '%')
                    ->orWhere('summary', 'like', '%' . $keyword . '%');
            });
        }

        // 如果有分类筛选 - 使用JOIN替代whereHas提升性能
        if ($filterCategory) {
            $category = $allCategories->firstWhere('url', $filterCategory);
            if ($category) {
                $categoryIds = [$category->id];
                // 如果是顶级分类，包含其所有子分类
                if ($category->fid == 0 && $category->children) {
                    $categoryIds = array_merge($categoryIds, $category->children->pluck('id')->toArray());
                }
                // 使用JOIN替代whereHas，大幅提升查询性能
                $query->join('list', 'event.id', '=', 'list.eid')
                      ->whereIn('list.cid', $categoryIds)
                      ->select('event.*')  // 添加这一行确保只返回event表的字段
                      ->distinct();
            }
        }

        // 如果有国家筛选 - 使用缓存避免重复查询
        if ($filterCountry) {
            // 使用静态缓存避免重复查询
            static $countryCache = null;
            if ($countryCache === null) {
                $countryCache = Country::all()->keyBy('url');
            }

            $country = $countryCache->get($filterCountry);
            if ($country) {
                $countryIds = [$country->id];
                // 如果是顶级国家（大洲），包含其所有子国家
                if ($country->fid == 0) {
                    $childCountries = Country::where('fid', $country->id)->pluck('id')->toArray();
                    $countryIds = array_merge($countryIds, $childCountries);
                }
                $query->whereIn('event.venue', $countryIds);
            }
        }

        // 如果有年份筛选
        if ($filterYear) {
            $yearStart = strtotime($filterYear . '-01-01 00:00:00');
            $yearEnd = strtotime($filterYear . '-12-31 23:59:59');
            $query->whereBetween('start_date', [$yearStart, $yearEnd]);
        }

        // 使用数据库级分页，大幅提升性能
        // 优化排序：即将召开的会议优先，按结束时间排序
        $conferences = $query
            ->orderByRaw('CASE WHEN event.end_date > ? THEN 0 ELSE 1 END', [$now])
            ->orderByRaw('CASE WHEN event.end_date > ? THEN event.end_date ELSE -event.end_date END', [$now])
            ->paginate($perPage);

        // 只为当前页的会议加载分类关系，避免N+1查询
        $eventIds = $conferences->pluck('id')->toArray();
        if (!empty($eventIds)) {
            $eventCategoryRelations = \DB::table('list')
                ->whereIn('eid', $eventIds)
                ->get(['eid', 'cid']);

            // 为每个会议分配其分类
            $eventCategoriesMap = [];
            foreach ($eventCategoryRelations as $relation) {
                if (!isset($eventCategoriesMap[$relation->eid])) {
                    $eventCategoriesMap[$relation->eid] = [];
                }
                // 只有当分类存在于缓存中时才添加
                if ($categoryMap->has($relation->cid)) {
                    $eventCategoriesMap[$relation->eid][] = $categoryMap[$relation->cid];
                }
            }

            // 将分类附加到会议对象
            foreach ($conferences as $conference) {
                $conference->categories = collect($eventCategoriesMap[$conference->id] ?? []);
            }
        }

        // 获取筛选选项数据（使用缓存）
        $categories = $this->categoryService->getTopCategories();
        $countries = $this->countryService->getTopLevelCountries();

        $filterOptions = [
            'categories' => $categories,
            'countries' => $countries,
            'years' => $this->getAvailableYears(),
        ];

        return [
            'conferences' => $conferences,
            'filterOptions' => $filterOptions,
            'currentFilters' => [
                'keyword' => $keyword,
                'category' => $filterCategory,
                'country' => $filterCountry,
                'year' => $filterYear,
            ],
        ];
    }

    /**
     * 获取按地点筛选的会议数据
     * 
     * @param string $url 地点URL标识
     * @param int $perPage 每页显示记录数
     * @return array 按地点筛选的会议数据
     * @throws \Exception 如果地点不存在
     */
    public function getVenueConferences(string $url, int $perPage = 10): array
    {
        // 根据URL标识查找地点
        $country = Country::where('url', $url)->firstOrFail();
        
        // 获取该地点及其子地点的ID列表
        $countryIds = [$country->id];
        $isContinent = false;
        $childCountries = collect();
        
        // 判断是否为大洲（顶级地区）
        if ($country->fid === 0) {
            
            $isContinent = true;
            
            // 获取大洲下的所有国家
            $childCountries = Country::where('fid', $country->id)
                ->select(['id', 'venue', 'url'])
                ->orderBy('listorder')
                ->get();
              
    
            
            // 将所有子国家ID添加到查询列表
            $countryIds = array_merge($countryIds, $childCountries->pluck('id')->toArray());
          
        }
        
        // 获取当前时间
        $now = time();
        
        // 基本查询条件
        $query = Event::published()
            ->whereIn('venue', $countryIds) // venue字段存储的是Country表的id
            ->with(['country:id,venue,url']);
            
        // 获取即将召开的会议
        $upcomingConferences = $query->where('end_date', '>=', $now)
            ->orderBy('end_date', 'asc')
            ->get();

        // 获取已结束的会议
        $pastQuery = clone $query;
        $pastConferences = $pastQuery->where('end_date', '<=', $now)
            ->orderBy('end_date', 'desc')
            ->get();

        // 合并会议列表
        $allConferences = $upcomingConferences->concat($pastConferences);

        // 手动分页
        $page = request()->get('page', 1);
        $offset = ($page - 1) * $perPage;
        $slicedConferences = $allConferences->slice($offset, $perPage);
        $conferences = new LengthAwarePaginator(
            $slicedConferences,
            $allConferences->count(),
            $perPage,
            $page,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        // 获取该地点下的特色会议
        $featuredConferences = Event::published()
            ->featured()
            ->where('end_date', '>=', $now)
            ->whereIn('venue', $countryIds) // venue存储的是Country表的id
            ->with(['country:id,venue,url'])
            ->orderBy('end_date', 'asc')
            ->get();

        // 获取广告
        $advertisements = Advertisement::where('cid', 0) // 使用首页广告
            ->where('endtime', '>=', $now)
            ->orderBy('id', 'desc')
            ->get();

        // 获取一级分类
        $categories = Category::where('fid', 0)->get();

        // SEO信息
        $placeholders = [
            'venue_name' => $country->venue,
            'site_name' => 'iConf'
        ];
        $seo = $this->seoService->getSeoData('conferences_list', $placeholders);

        return [
            'country' => $country,
            'conferences' => $conferences,
            'featuredConferences' => $featuredConferences,
            'advertisements' => $advertisements,
            'categories' => $categories,
            'childCountries' => $childCountries,  // 大洲的子国家列表
            'isContinent' => $isContinent,        // 是否为大洲
            'totalCount' => $allConferences->count(), // 会议总数
            'seo' => $seo
        ];
    }

    /**
     * 获取可用的年份列表（用于筛选）
     *
     * @return array 年份数组
     */
    private function getAvailableYears(): array
    {
        // 使用缓存避免重复查询
        static $years = null;
        if ($years === null) {
            $currentYear = date('Y');
            $years = [];

            // 提供当前年份前后各3年的选项
            for ($i = -2; $i <= 3; $i++) {
                $year = $currentYear + $i;
                $years[] = [
                    'value' => $year,
                    'label' => $year,
                ];
            }
        }

        return $years;
    }
}
