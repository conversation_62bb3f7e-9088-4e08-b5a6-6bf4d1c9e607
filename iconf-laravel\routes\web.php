<?php

use App\Http\Controllers\TestFragmentController;
use App\Http\Controllers\HtmlFragmentPreviewController;
use App\Http\Controllers\Frontend\ConferenceController;
use App\Http\Controllers\Frontend\ConferenceCardController;
use App\Http\Controllers\Frontend\HomeController;
use App\Http\Controllers\Frontend\PageController;
use Illuminate\Support\Facades\Route;

// 前台首页路由
Route::get('/', [HomeController::class, 'index'])->name('home');

// 验证码路由
Route::get('captcha/{config?}', [\Mews\Captcha\CaptchaController::class, 'getCaptcha'])->name('captcha');
// 验证码验证路由
Route::post('validate-captcha', [\App\Http\Controllers\CaptchaController::class, 'validateCaptcha'])->name('validate.captcha');

// HTML碎片测试路由
Route::get('/test-fragment', [TestFragmentController::class, 'index'])->name('test.fragment');
Route::get('/test-fragment/helper', [TestFragmentController::class, 'testHelper'])->name('test.helper');

// HTML碎片预览路由
Route::get('/html-fragments/preview/{id}', [HtmlFragmentPreviewController::class, 'preview'])->name('html-fragments.preview');

// 前台会议详情页路由
Route::get('/conference/{url}', [ConferenceController::class, 'show'])->name('conference.show');

// 前台分类会议列表页路由
Route::get('/categories/{url}', [HomeController::class, 'lists'])->name('categories.lists');

// 前台所有会议列表页路由
Route::get('/conferences', [HomeController::class, 'conferences'])->name('conferences.index');

// 前台按地点筛选会议列表路由
Route::get('/venues/{url}', [HomeController::class, 'venueConferences'])->name('venues.conferences');

// 新闻相关路由
Route::get('/news', [\App\Http\Controllers\Frontend\NewsController::class, 'index'])->name('news.index');
Route::get('/news/{id}', [\App\Http\Controllers\Frontend\NewsController::class, 'show'])->name('news.show');

// 单页面路由
Route::get('page/{identifier}', [PageController::class, 'show'])->name('page.show');

// 测试路由 - 用于调试分类URL问题
Route::get('/test-category/{url}', function ($url) {
    try {
        $startTime = microtime(true);
        $homeService = app(\App\Services\Frontend\HomeService::class);
        $data = $homeService->getCategoryConferences($url, 5);
        $endTime = microtime(true);

        $executionTime = round(($endTime - $startTime) * 1000, 2);

        return response()->json([
            'success' => true,
            'category' => [
                'id' => $data['category']->id,
                'name' => $data['category']->name,
                'url' => $data['category']->url,
                'fid' => $data['category']->fid,
            ],
            'conferences_count' => $data['conferences']->count(),
            'conferences_total' => $data['conferences']->total(),
            'featured_conferences_count' => $data['featuredConferences']->count(),
            'advertisements_count' => $data['advertisements']->count(),
            'execution_time_ms' => $executionTime,
            'message' => 'Category found successfully'
        ]);
    } catch (\App\Exceptions\CategoryNotFoundException $e) {
        return response()->json([
            'success' => false,
            'error' => 'Category not found',
            'message' => $e->getMessage(),
            'url' => $url,
            'category_url' => $e->getCategoryUrl()
        ], 404);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => 'Server error',
            'message' => $e->getMessage(),
            'url' => $url,
            'exception_type' => get_class($e)
        ], 500);
    }
})->name('test.category');

// 测试会议卡片路由
Route::get('/test-conference-cards', function () {
    try {
        $service = new App\Services\ConferenceCardService();
        $featuredConferences = $service->getFeaturedConferences(2);
        $upcomingConferences = $service->getUpcomingConferences(2);

        return view('test-conference-cards', compact('featuredConferences', 'upcomingConferences'));
    } catch (\Exception $e) {
        return response()->json(['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
    }
})->name('test.conference.cards');

// 简单测试倒计时组件
Route::get('/test-countdown', function () {
    try {
        $conference = App\Models\Event::where('status', 1)->first();
        if (!$conference) {
            return response('No conference found', 404);
        }
        return view('test-countdown', compact('conference'));
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// 最简单的测试
Route::get('/test-simple', function () {
    return '<h1>Simple Test Works!</h1>';
});

// 友情链接测试路由
Route::get('/test-links', function () {
    return view('test-links');
})->name('test.links');

// 兼容老系统的新闻列表翻页URL - 不区分大小写
Route::get('/home/<USER>/news/m/{m}/p/{page}.html', [\App\Http\Controllers\Frontend\NewsController::class, 'legacyPagination'])
    ->name('news.legacy.pagination');

// 兼容老系统的新闻列表翻页URL - 大写形式
Route::get('/Home/Index/news/m/{m}/p/{page}.html', [\App\Http\Controllers\Frontend\NewsController::class, 'legacyPagination']);

// 会议卡片演示路由
Route::prefix('conference-cards')->group(function () {
    Route::get('/demo', [ConferenceCardController::class, 'demo'])->name('conference-cards.demo');
    Route::get('/load-more-featured', [ConferenceCardController::class, 'loadMoreFeatured'])->name('conference-cards.load-more-featured');
    Route::get('/load-more-upcoming', [ConferenceCardController::class, 'loadMoreUpcoming'])->name('conference-cards.load-more-upcoming');
    Route::get('/statistics', [ConferenceCardController::class, 'statistics'])->name('conference-cards.statistics');
    Route::post('/clear-cache', [ConferenceCardController::class, 'clearCache'])->name('conference-cards.clear-cache');
});

// Sitemap route
Route::get('/sitemap', function () {
    return response(file_get_contents(public_path('sitemap.xml')), 200, [
        'Content-Type' => 'application/xml'
    ]);
});

